package analytics

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"
	meetanalyticsrpcclient "seevision.cn/server/meet-analytics-rpc/meetanalyticsrpcclient"
	"seevision.cn/server/meet-analytics-rpc/types/meetanalyticsrpc"

	"github.com/suyuan32/simple-admin-common/utils/pointy"
	"github.com/zeromicro/go-zero/core/logx"
)

type BatchProcessingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchProcessingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchProcessingLogic {
	return &BatchProcessingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// BatchConfig 批量处理配置
type BatchConfig struct {
	MaxBatchSize   int           `json:"max_batch_size"`  // 最大批次大小
	BatchTimeout   time.Duration `json:"batch_timeout"`   // 批次超时时间
	MaxConcurrency int           `json:"max_concurrency"` // 最大并发数
	RetryAttempts  int           `json:"retry_attempts"`  // 重试次数
	RetryDelay     time.Duration `json:"retry_delay"`     // 重试延迟
	EnableAsync    bool          `json:"enable_async"`    // 启用异步处理
	FlushInterval  time.Duration `json:"flush_interval"`  // 刷新间隔
}

// BatchItem 批次项
type BatchItem struct {
	Index     int              `json:"index"`
	Event     *types.EventItem `json:"event"`
	ClientIP  string           `json:"client_ip"`
	UserAgent string           `json:"user_agent"`
	ProcessAt time.Time        `json:"process_at"`
}

// BatchResult 批次处理结果
type BatchResult struct {
	TotalCount   int                `json:"total_count"`
	SuccessCount int                `json:"success_count"`
	FailedCount  int                `json:"failed_count"`
	ProcessTime  time.Duration      `json:"process_time"`
	StartTime    time.Time          `json:"start_time"`
	EndTime      time.Time          `json:"end_time"`
	Errors       []types.EventError `json:"errors"`
}

// EventBatch 事件批次
type EventBatch struct {
	ID        string      `json:"id"`
	Items     []BatchItem `json:"items"`
	CreatedAt time.Time   `json:"created_at"`
	Config    BatchConfig `json:"config"`
	Status    string      `json:"status"` // pending, processing, completed, failed
}

// BatchProcessor 批量处理器
type BatchProcessor struct {
	config     BatchConfig
	queue      chan *EventBatch
	workerPool chan struct{}
	results    chan *BatchResult
	mu         sync.RWMutex
	isRunning  bool
	logic      *BatchProcessingLogic
}

// GetDefaultBatchConfig 获取默认批量处理配置
func (l *BatchProcessingLogic) GetDefaultBatchConfig() BatchConfig {
	return BatchConfig{
		MaxBatchSize:   50,
		BatchTimeout:   5 * time.Second,
		MaxConcurrency: 10,
		RetryAttempts:  3,
		RetryDelay:     time.Second,
		EnableAsync:    true,
		FlushInterval:  2 * time.Second,
	}
}

// NewBatchProcessor 创建批量处理器
func (l *BatchProcessingLogic) NewBatchProcessor(config BatchConfig) *BatchProcessor {
	processor := &BatchProcessor{
		config:     config,
		queue:      make(chan *EventBatch, 100),
		workerPool: make(chan struct{}, config.MaxConcurrency),
		results:    make(chan *BatchResult, 100),
		logic:      l,
	}

	// 初始化工作池
	for i := 0; i < config.MaxConcurrency; i++ {
		processor.workerPool <- struct{}{}
	}

	return processor
}

// ProcessEventsBatch 批量处理事件
func (l *BatchProcessingLogic) ProcessEventsBatch(events []types.EventItem, clientIP, userAgent string, appId uint64) (*BatchResult, error) {
	startTime := time.Now()

	l.Infof("开始批量处理事件，数量: %d, AppID: %d, ClientIP: %s", len(events), appId, clientIP)

	result := &BatchResult{
		TotalCount:   len(events),
		SuccessCount: 0,
		FailedCount:  0,
		StartTime:    startTime,
		Errors:       []types.EventError{},
	}

	// 如果事件数量较少，使用同步处理
	if len(events) <= 10 {
		return l.processSyncBatch(events, clientIP, userAgent, appId, result)
	}

	// 使用异步批量处理
	return l.processAsyncBatch(events, clientIP, userAgent, appId, result)
}

// processSyncBatch 同步批量处理
func (l *BatchProcessingLogic) processSyncBatch(events []types.EventItem, clientIP, userAgent string, appId uint64, result *BatchResult) (*BatchResult, error) {
	// 处理每个事件
	for i, event := range events {
		if err := l.processSingleEvent(i, &event, clientIP, userAgent, appId); err != nil {
			l.Errorf("处理事件失败，索引: %d, 错误: %v", i, err)
			result.FailedCount++
			result.Errors = append(result.Errors, types.EventError{
				Index:        int32(i),
				ErrorCode:    "PROCESS_ERROR",
				ErrorMessage: err.Error(),
			})
		} else {
			result.SuccessCount++
		}
	}

	result.EndTime = time.Now()
	result.ProcessTime = result.EndTime.Sub(result.StartTime)

	l.Infof("同步批量处理完成，成功: %d, 失败: %d, 耗时: %v",
		result.SuccessCount, result.FailedCount, result.ProcessTime)

	return result, nil
}

// processAsyncBatch 异步批量处理
func (l *BatchProcessingLogic) processAsyncBatch(events []types.EventItem, clientIP, userAgent string, appId uint64, result *BatchResult) (*BatchResult, error) {
	batchSize := 20     // 每批处理20个事件
	maxConcurrency := 5 // 最大并发数

	l.Infof("启动异步批量处理，事件数量: %d, 批次大小: %d", len(events), batchSize)

	// 分割为多个批次
	batches := l.splitIntoBatches(events, batchSize)

	// 使用工作池处理批次
	return l.processBatchesWithWorkerPool(batches, clientIP, userAgent, appId, maxConcurrency, result)
}

// splitIntoBatches 分割为批次
func (l *BatchProcessingLogic) splitIntoBatches(events []types.EventItem, batchSize int) [][]types.EventItem {
	var batches [][]types.EventItem

	for i := 0; i < len(events); i += batchSize {
		end := i + batchSize
		if end > len(events) {
			end = len(events)
		}
		batches = append(batches, events[i:end])
	}

	l.Infof("分割为 %d 个批次，每批最多 %d 个事件", len(batches), batchSize)
	return batches
}

// processBatchesWithWorkerPool 使用工作池处理批次
func (l *BatchProcessingLogic) processBatchesWithWorkerPool(batches [][]types.EventItem, clientIP, userAgent string, appId uint64, maxConcurrency int, result *BatchResult) (*BatchResult, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 创建工作池
	workerPool := make(chan struct{}, maxConcurrency)
	for i := 0; i < maxConcurrency; i++ {
		workerPool <- struct{}{}
	}

	// 处理每个批次
	for batchIndex, batch := range batches {
		wg.Add(1)
		go func(idx int, events []types.EventItem) {
			defer wg.Done()

			// 获取工作许可
			<-workerPool
			defer func() {
				workerPool <- struct{}{}
			}()

			l.Infof("开始处理批次 %d，事件数量: %d", idx, len(events))

			// 处理批次中的每个事件
			for i, event := range events {
				globalIndex := idx*20 + i // 计算全局索引
				if err := l.processSingleEvent(globalIndex, &event, clientIP, userAgent, appId); err != nil {
					l.Errorf("批次 %d 中事件处理失败，索引: %d, 错误: %v", idx, i, err)

					mu.Lock()
					result.FailedCount++
					result.Errors = append(result.Errors, types.EventError{
						Index:        int32(globalIndex),
						ErrorCode:    "BATCH_PROCESS_ERROR",
						ErrorMessage: err.Error(),
					})
					mu.Unlock()
				} else {
					mu.Lock()
					result.SuccessCount++
					mu.Unlock()
				}
			}

			l.Infof("批次 %d 处理完成", idx)
		}(batchIndex, batch)
	}

	// 等待所有批次完成
	wg.Wait()

	result.EndTime = time.Now()
	result.ProcessTime = result.EndTime.Sub(result.StartTime)

	l.Infof("异步批量处理完成，成功: %d, 失败: %d, 耗时: %v",
		result.SuccessCount, result.FailedCount, result.ProcessTime)

	return result, nil
}

// processSingleEvent 处理单个事件
func (l *BatchProcessingLogic) processSingleEvent(index int, event *types.EventItem, clientIP, userAgent string, appId uint64) error {
	// 1. 验证必填字段
	if err := l.validateEvent(event); err != nil {
		return fmt.Errorf("事件验证失败: %w", err)
	}

	// 2. 标准化事件数据
	if err := l.normalizeEvent(event, clientIP, userAgent); err != nil {
		return fmt.Errorf("事件标准化失败: %w", err)
	}

	// 3. 根据事件类型进行特殊处理
	if err := l.handleEventByType(event, appId); err != nil {
		return fmt.Errorf("事件类型处理失败: %w", err)
	}

	// 4. 存储事件数据
	if err := l.storeEvent(event, appId, clientIP, userAgent); err != nil {
		return fmt.Errorf("事件存储失败: %w", err)
	}

	return nil
}

// validateEvent 验证事件数据
func (l *BatchProcessingLogic) validateEvent(event *types.EventItem) error {
	if event.EventName == "" {
		return fmt.Errorf("EventName不能为空")
	}
	if event.DeviceId == "" {
		return fmt.Errorf("DeviceId不能为空")
	}
	return nil
}

// normalizeEvent 标准化事件数据
func (l *BatchProcessingLogic) normalizeEvent(event *types.EventItem, clientIP, userAgent string) error {
	// 标准化时间戳
	if event.Timestamp == nil {
		now := time.Now().UnixMilli()
		event.Timestamp = &now
	}

	// 标准化事件名称（转小写，替换空格为下划线）
	event.EventName = strings.ToLower(strings.ReplaceAll(event.EventName, " ", "_"))

	// 自动填充IP地址（如果未提供）
	if event.IpAddress == nil || *event.IpAddress == "" {
		event.IpAddress = &clientIP
	}

	// 自动填充User-Agent（如果未提供）
	if event.UserAgent == nil || *event.UserAgent == "" {
		event.UserAgent = &userAgent
	}

	// 验证并解析自定义属性JSON格式
	if event.CustomProperties != nil && *event.CustomProperties != "" {
		var props map[string]interface{}
		if err := json.Unmarshal([]byte(*event.CustomProperties), &props); err != nil {
			return fmt.Errorf("CustomProperties格式错误: %w", err)
		}
	}

	return nil
}

// handleEventByType 根据事件类型进行特殊处理
func (l *BatchProcessingLogic) handleEventByType(event *types.EventItem, appId uint64) error {
	// 根据事件名称判断事件类型（优先从事件定义表查询）
	eventType := l.getEventTypeFromName(event.EventName, appId)

	switch eventType {
	case "user_action":
		return l.handleUserActionEvent(event)
	case "business_event":
		return l.handleBusinessEvent(event)
	case "system_event":
		return l.handleSystemEvent(event)
	case "marketing_event":
		return l.handleMarketingEvent(event)
	case "product_analytics":
		return l.handleProductAnalyticsEvent(event)
	default:
		// 未知类型事件，记录警告但不阻止处理
		l.Infof("未知事件类型: %s，将按默认方式处理", eventType)
	}
	return nil
}

// handleUserActionEvent 处理用户行为事件
func (l *BatchProcessingLogic) handleUserActionEvent(event *types.EventItem) error {
	l.Infof("处理用户行为事件: %s", event.EventName)
	// 用户行为事件的特殊处理逻辑
	// ...
	return nil
}

// handleBusinessEvent 处理业务事件
func (l *BatchProcessingLogic) handleBusinessEvent(event *types.EventItem) error {
	l.Infof("处理业务事件: %s", event.EventName)
	// 业务事件的特殊处理逻辑
	// ...
	return nil
}

// handleSystemEvent 处理系统事件
func (l *BatchProcessingLogic) handleSystemEvent(event *types.EventItem) error {
	l.Infof("处理系统事件: %s", event.EventName)
	// 系统事件的特殊处理逻辑
	// ...
	return nil
}

// handleMarketingEvent 处理营销事件
func (l *BatchProcessingLogic) handleMarketingEvent(event *types.EventItem) error {
	l.Infof("处理营销事件: %s", event.EventName)
	// 营销事件的特殊处理逻辑
	// 例如：广告点击、横幅展示、推广活动等
	return nil
}

// handleProductAnalyticsEvent 处理产品分析事件
func (l *BatchProcessingLogic) handleProductAnalyticsEvent(event *types.EventItem) error {
	l.Infof("处理产品分析事件: %s", event.EventName)
	// 产品分析事件的特殊处理逻辑
	// 例如：功能使用、产品交互等
	return nil
}

// storeEvent 存储事件数据（同步事务化处理）
func (l *BatchProcessingLogic) storeEvent(event *types.EventItem, appId uint64, clientIP, userAgent string) error {
	l.Infof("开始同步存储事件 - AppID: %d, EventName: %s, DeviceId: %s", appId, event.EventName, event.DeviceId)

	// 1. 同步更新设备信息（使用upsert方法）
	if err := l.upsertDeviceInfo(event, appId); err != nil {
		l.Errorf("同步更新设备信息失败, DeviceId: %s, Error: %v", event.DeviceId, err)
		return fmt.Errorf("设备信息upsert失败: %w", err)
	}
	l.Infof("设备信息同步更新成功, DeviceId: %s", event.DeviceId)

	// 2. 同步更新用户信息（如果有用户ID，使用upsert方法）
	if event.UserId != nil && *event.UserId > 0 {
		if err := l.upsertUserInfo(event, appId); err != nil {
			l.Errorf("同步更新用户信息失败, UserId: %d, Error: %v", *event.UserId, err)
			return fmt.Errorf("用户信息upsert失败: %w", err)
		}
		l.Infof("用户信息同步更新成功, UserId: %d", *event.UserId)
	}

	// 3. 将API事件类型转换为RPC事件类型
	l.Infof("开始转换事件类型为RPC格式")
	rpcEvent := l.convertToRpcEventInfo(event, appId)

	// 添加字段映射调试日志
	l.Infof("字段映射调试 - API字段: DeviceBrand=%v, DeviceModel=%v, Platform=%v, Country=%v",
		event.DeviceBrand, event.DeviceModel, event.Platform, event.Country)
	l.Infof("字段映射调试 - RPC字段: DeviceBrand=%v, DeviceModel=%v, Platform=%v, Country=%v",
		rpcEvent.DeviceBrand, rpcEvent.DeviceModel, rpcEvent.Platform, rpcEvent.Country)

	l.Infof("事件类型转换完成，准备调用RPC存储")

	// 4. 调用RPC存储事件
	_, err := l.svcCtx.MeetAnalyticsRpc.CreateAnalyticsEvent(l.ctx, rpcEvent)
	if err != nil {
		l.Errorf("存储事件失败, AppID: %d, EventName: %s, Error: %v", appId, event.EventName, err)
		// 可以在此处触发备份机制
		l.storeEventBackup(event, clientIP, userAgent)
		return fmt.Errorf("RPC存储失败: %w", err)
	}

	l.Infof("事件存储成功, AppID: %d, EventName: %s", appId, event.EventName)
	return nil
}

// storeEventBackup 备份事件数据
func (l *BatchProcessingLogic) storeEventBackup(event *types.EventItem, clientIP, userAgent string) error {
	// 实现备份逻辑，例如写入本地文件或备用存储
	l.Errorf("event storage failed, starting backup mechanism, event name: %s", event.EventName)
	return nil
}

// ensureUserExists 确保用户存在（已通过upsertUserInfo实现）
func (l *BatchProcessingLogic) ensureUserExists(event *types.EventItem, appId uint64) error {
	if event.UserId == nil || *event.UserId == 0 {
		return nil // 没有用户ID，无需处理
	}
	// 用户信息已在storeEvent中通过upsertUserInfo同步处理
	l.Debugf("用户存在性检查完成，用户ID: %d", *event.UserId)
	return nil
}

// ensureDeviceExists 确保设备存在（已通过upsertDeviceInfo实现）
func (l *BatchProcessingLogic) ensureDeviceExists(event *types.EventItem, appId uint64) error {
	if event.DeviceId == "" {
		return nil // 没有设备ID，无需处理
	}
	// 设备信息已在storeEvent中通过upsertDeviceInfo同步处理
	l.Debugf("设备存在性检查完成，设备ID: %s", event.DeviceId)
	return nil
}

// convertToRpcEventInfo 将API事件类型转换为RPC事件类型
func (l *BatchProcessingLogic) convertToRpcEventInfo(event *types.EventItem, appId uint64) *meetanalyticsrpcclient.AnalyticsEventInfo {
	// 根据事件名称获取事件类型（优先从事件定义表查询）
	eventType := l.getEventTypeFromName(event.EventName, appId)

	rpcEvent := &meetanalyticsrpcclient.AnalyticsEventInfo{
		AppId:     &appId,
		UserId:    event.UserId,
		DeviceId:  &event.DeviceId,
		EventName: &event.EventName,
		EventType: &eventType, // 使用生成的事件类型
		Timestamp: event.Timestamp,
		IpAddress: event.IpAddress,
		UserAgent: event.UserAgent,
	}

	// 只有当字段有值时才设置，避免传递空值
	// 设备相关字段
	if event.Platform != nil && *event.Platform != "" {
		rpcEvent.Platform = event.Platform
	}
	if event.AppVersion != nil && *event.AppVersion != "" {
		rpcEvent.AppVersion = event.AppVersion
	}
	if event.OsVersion != nil && *event.OsVersion != "" {
		rpcEvent.OsVersion = event.OsVersion
	}
	if event.DeviceBrand != nil && *event.DeviceBrand != "" {
		rpcEvent.DeviceBrand = event.DeviceBrand
	}
	if event.DeviceModel != nil && *event.DeviceModel != "" {
		rpcEvent.DeviceModel = event.DeviceModel
	}

	// 位置相关字段
	if event.Country != nil && *event.Country != "" {
		rpcEvent.Country = event.Country
	}
	if event.Province != nil && *event.Province != "" {
		rpcEvent.Province = event.Province
	}
	if event.City != nil && *event.City != "" {
		rpcEvent.City = event.City
	}
	if event.Latitude != nil && *event.Latitude != 0.0 {
		rpcEvent.Latitude = event.Latitude
	}
	if event.Longitude != nil && *event.Longitude != 0.0 {
		rpcEvent.Longitude = event.Longitude
	}

	// 页面和UTM相关字段
	if event.PageUrl != nil && *event.PageUrl != "" {
		rpcEvent.PageUrl = event.PageUrl
	}
	if event.PageTitle != nil && *event.PageTitle != "" {
		rpcEvent.PageTitle = event.PageTitle
	}
	if event.ReferrerUrl != nil && *event.ReferrerUrl != "" {
		rpcEvent.Referrer = event.ReferrerUrl
	}
	if event.UtmSource != nil && *event.UtmSource != "" {
		rpcEvent.UtmSource = event.UtmSource
	}
	if event.UtmMedium != nil && *event.UtmMedium != "" {
		rpcEvent.UtmMedium = event.UtmMedium
	}
	if event.UtmCampaign != nil && *event.UtmCampaign != "" {
		rpcEvent.UtmCampaign = event.UtmCampaign
	}
	if event.UtmContent != nil && *event.UtmContent != "" {
		rpcEvent.UtmContent = event.UtmContent
	}
	if event.UtmTerm != nil && *event.UtmTerm != "" {
		rpcEvent.UtmTerm = event.UtmTerm
	}

	// 构建事件属性JSON
	eventProps := make(map[string]interface{})
	// 添加事件名称作为基础属性
	eventProps["event_name"] = event.EventName

	// 优先处理 EventProperties 字段
	if event.EventProperties != nil && *event.EventProperties != "" {
		var eventPropsData map[string]interface{}
		if err := json.Unmarshal([]byte(*event.EventProperties), &eventPropsData); err == nil {
			for k, v := range eventPropsData {
				eventProps[k] = v
			}
			l.Infof("成功解析 EventProperties: %s", *event.EventProperties)
		} else {
			l.Errorf("Failed to unmarshal event properties, error: %v, properties: %s", err, *event.EventProperties)
		}
	}

	// 如果有自定义属性，合并到事件属性中（EventProperties 优先级更高）
	if event.CustomProperties != nil && *event.CustomProperties != "" {
		var customProps map[string]interface{}
		if err := json.Unmarshal([]byte(*event.CustomProperties), &customProps); err == nil {
			for k, v := range customProps {
				// 只有当 EventProperties 中没有这个键时才添加
				if _, exists := eventProps[k]; !exists {
					eventProps[k] = v
				}
			}
			l.Infof("成功解析 CustomProperties: %s", *event.CustomProperties)
		} else {
			l.Errorf("Failed to unmarshal custom properties, error: %v, properties: %s", err, *event.CustomProperties)
		}
	}

	if len(eventProps) > 0 {
		if propsBytes, err := json.Marshal(eventProps); err == nil {
			propsStr := string(propsBytes)
			rpcEvent.EventProperties = &propsStr
		} else {
			l.Errorf("Failed to marshal event properties, error: %v", err)
		}
	}

	return rpcEvent
}

// upsertDeviceInfo 更新或创建设备信息（使用upsert方法）
func (l *BatchProcessingLogic) upsertDeviceInfo(event *types.EventItem, appId uint64) error {
	// 构建设备信息
	deviceInfo := &meetanalyticsrpcclient.AnalyticsDeviceInfo{
		AppId:        &appId,
		DeviceId:     &event.DeviceId,
		Platform:     event.Platform,
		DeviceBrand:  event.DeviceBrand,
		DeviceModel:  event.DeviceModel,
		OsVersion:    event.OsVersion,
		AppVersion:   event.AppVersion,
		Country:      event.Country,
		Province:     event.Province,
		City:         event.City,
		LastSeenTime: event.Timestamp,
	}

	// 设置首次见到时间（如果是新设备）
	deviceInfo.FirstSeenTime = event.Timestamp

	// 调用RPC upsert设备信息
	_, err := l.svcCtx.MeetAnalyticsRpc.UpsertAnalyticsDevice(l.ctx, deviceInfo)
	if err != nil {
		l.Errorf("Upsert设备信息失败: %v", err)
		return err
	}

	return nil
}

// upsertUserInfo 更新或创建用户信息（使用upsert方法）
func (l *BatchProcessingLogic) upsertUserInfo(event *types.EventItem, appId uint64) error {
	// 构建用户信息
	userInfo := &meetanalyticsrpcclient.AnalyticsUserInfo{
		AppId:          &appId,
		UserId:         event.UserId,
		Country:        event.Country,
		Province:       event.Province,
		City:           event.City,
		LastVisitTime:  event.Timestamp,
		FirstVisitTime: event.Timestamp, // 如果是新用户，设置首次访问时间
	}

	// 调用RPC upsert用户信息
	_, err := l.svcCtx.MeetAnalyticsRpc.UpsertAnalyticsUser(l.ctx, userInfo)
	if err != nil {
		l.Errorf("Upsert用户信息失败: %v", err)
		return err
	}

	return nil
}

// GetBatchProcessingStats 获取批量处理统计信息
func (l *BatchProcessingLogic) GetBatchProcessingStats() map[string]interface{} {
	return map[string]interface{}{
		"default_config": l.GetDefaultBatchConfig(),
		"timestamp":      time.Now().UnixMilli(),
		"status":         "active",
	}
}

// 辅助函数

// contains 检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr ||
		len(s) >= len(substr) && s[:len(substr)] == substr ||
		(len(s) > len(substr) && func() bool {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
			return false
		}())
}

// getEventTypeFromName 根据事件名称获取事件类型，优先从事件定义表查询
func (l *BatchProcessingLogic) getEventTypeFromName(eventName string, appId uint64) string {
	// 先从事件定义表查询
	req := &meetanalyticsrpc.AnalyticsEventDefinitionListReq{
		Page:      1,
		PageSize:  1,
		AppId:     &appId,
		EventName: &eventName,
		Status:    pointy.GetPointer(uint32(1)), // 只查询启用的事件
	}

	resp, err := l.svcCtx.MeetAnalyticsRpc.GetAnalyticsEventDefinitionList(l.ctx, req)
	if err != nil {
		l.Errorf("查询事件定义失败: %v, eventName: %s, appId: %d", err, eventName, appId)
	} else if resp != nil && len(resp.Data) > 0 {
		// 找到事件定义，使用定义表中的事件类型
		eventType := resp.Data[0].EventType
		if eventType != nil && *eventType != "" {
			l.Infof("从事件定义表获取事件类型: eventName=%s, eventType=%s", eventName, *eventType)
			return *eventType
		}
	}

	// 如果没有找到事件定义，使用默认分类逻辑
	l.Infof("事件定义表中未找到 %s，使用默认分类逻辑", eventName)

	// 用户行为事件
	if contains(eventName, "page_view") || contains(eventName, "click") ||
		contains(eventName, "form_submit") || contains(eventName, "scroll") ||
		contains(eventName, "search") || contains(eventName, "print") ||
		contains(eventName, "copy") || contains(eventName, "feature_used") {
		return "user_action"
	}

	// 业务事件
	if contains(eventName, "purchase") || contains(eventName, "add_to_cart") ||
		contains(eventName, "conversion") || contains(eventName, "registration") ||
		contains(eventName, "subscription") {
		return "business_event"
	}

	// 系统事件
	if contains(eventName, "app_start") || contains(eventName, "app_launch") ||
		contains(eventName, "app_crash") || contains(eventName, "error") ||
		contains(eventName, "performance") || contains(eventName, "network_status") ||
		contains(eventName, "app_install") {
		return "system_event"
	}

	// 营销事件
	if contains(eventName, "banner_") || contains(eventName, "ad_") ||
		contains(eventName, "campaign_") || contains(eventName, "promotion_") {
		return "marketing_event"
	}

	// 默认归类为用户行为
	return "user_action"
}
