package base

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"seevision.cn/server/meet-analytics-api/internal/logic/base"
	"seevision.cn/server/meet-analytics-api/internal/svc"
)

func InitDatabaseHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := base.NewInitDatabaseLogic(r.Context(), svcCtx)
		resp, err := l.InitDatabase()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
