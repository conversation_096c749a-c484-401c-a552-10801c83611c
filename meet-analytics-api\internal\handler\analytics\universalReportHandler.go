package analytics

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"seevision.cn/server/meet-analytics-api/internal/logic/analytics"
	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"
)

func UniversalReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UniversalReportReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := analytics.NewUniversalReportLogic(r.Context(), svcCtx)
		resp, err := l.UniversalReport(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
