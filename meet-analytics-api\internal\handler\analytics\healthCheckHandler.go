package analytics

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"seevision.cn/server/meet-analytics-api/internal/logic/analytics"
	"seevision.cn/server/meet-analytics-api/internal/svc"
)

func HealthCheckHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := analytics.NewHealthCheckLogic(r.Context(), svcCtx)
		resp, err := l.HealthCheck()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
