// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic ID request (string) | 基础ID参数请求 (string)
// swagger:model IDStringReq
type IDStringReq struct {
	// ID
	// Required: true
	Id string `json:"id"`
}

// Basic IDs request (string) | 基础ID数组参数请求 (string)
// swagger:model IDsStringReq
type IDsStringReq struct {
	// IDs
	// Required: true
	Ids []string `json:"ids"`
}

// Basic ID request (string) | 基础ID地址参数请求 (string)
// swagger:model IDStringPathReq
type IDStringPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (string) | 基础ID信息 (string)
// swagger:model BaseIDStringInfo
type BaseIDStringInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// Empty request | 无参数请求
// swagger:model EmptyReq
type EmptyReq struct {
}

// 通用事件上报请求 | Universal event report request
// swagger:model UniversalReportReq
type UniversalReportReq struct {
	// 事件列表 | Event list
	// required : true
	// min length : 1
	// max length : 100
	Events []EventItem `json:"events" validate:"required,min=1,max=100"`
}

// 事件项 | Event item
type EventItem struct {
	// 事件名称 | Event name
	// required : true
	EventName string `json:"eventName" validate:"required"`
	// 用户ID | User ID
	UserId *uint64 `json:"userId,optional"`
	// 设备ID | Device ID
	// required : true
	DeviceId string `json:"deviceId" validate:"required"`
	// 页面URL | Page URL
	PageUrl *string `json:"pageUrl,optional"`
	// 页面标题 | Page title
	PageTitle *string `json:"pageTitle,optional"`
	// 来源页面 | Referrer URL
	ReferrerUrl *string `json:"referrerUrl,optional"`
	// UTM来源 | UTM source
	UtmSource *string `json:"utmSource,optional"`
	// UTM媒介 | UTM medium
	UtmMedium *string `json:"utmMedium,optional"`
	// UTM活动 | UTM campaign
	UtmCampaign *string `json:"utmCampaign,optional"`
	// UTM关键词 | UTM term
	UtmTerm *string `json:"utmTerm,optional"`
	// UTM内容 | UTM content
	UtmContent *string `json:"utmContent,optional"`
	// 事件属性 | Event properties (JSON格式)
	EventProperties *string `json:"eventProperties,optional"`
	// 自定义属性 | Custom properties (JSON格式)
	CustomProperties *string `json:"customProperties,optional"`
	// 事件发生时间戳(毫秒) | Event timestamp in milliseconds
	Timestamp *int64 `json:"timestamp,optional"`
	// IP地址 | IP address (服务端可自动获取)
	IpAddress *string `json:"ipAddress,optional"`
	// User Agent (服务端可自动获取)
	UserAgent *string `json:"userAgent,optional"`
	// 应用版本 | App version
	AppVersion *string `json:"appVersion,optional"`
	// 操作系统版本 | OS version
	OsVersion *string `json:"osVersion,optional"`
	// 平台 | Platform
	Platform *string `json:"platform,optional"`
	// 设备品牌 | Device brand (Apple, Huawei, Xiaomi, Samsung等)
	DeviceBrand *string `json:"deviceBrand,optional"`
	// 设备型号 | Device model (iPhone 13, Huawei P40, Xiaomi Mi 11等)
	DeviceModel *string `json:"deviceModel,optional"`
	// 国家 | Country
	Country *string `json:"country,optional"`
	// 省份 | Province
	Province *string `json:"province,optional"`
	// 城市 | City
	City *string `json:"city,optional"`
	// 纬度 | Latitude
	Latitude *float64 `json:"latitude,optional"`
	// 经度 | Longitude
	Longitude *float64 `json:"longitude,optional"`
}

// 事件上报响应 | Event report response
// swagger:model EventReportResp
type EventReportResp struct {
	BaseDataInfo
	// 处理结果 | Processing result
	Data EventReportResult `json:"data"`
}

// 事件上报结果 | Event report result
type EventReportResult struct {
	// 成功处理的事件数量 | Successfully processed events count
	SuccessCount int32 `json:"successCount"`
	// 失败的事件数量 | Failed events count
	FailedCount int32 `json:"failedCount"`
	// 处理时间戳 | Processing timestamp
	ProcessedAt int64 `json:"processedAt"`
	// 错误信息 | Error details
	Errors []EventError `json:"errors,optional"`
}

// 事件错误信息 | Event error info
type EventError struct {
	// 事件索引 | Event index
	Index int32 `json:"index"`
	// 错误码 | Error code
	ErrorCode string `json:"errorCode"`
	// 错误信息 | Error message
	ErrorMessage string `json:"errorMessage"`
}

// 健康检查响应 | Health check response
// swagger:model HealthCheckResp
type HealthCheckResp struct {
	// 状态 | Status
	Status string `json:"status"`
	// 时间戳 | Timestamp
	Timestamp int64 `json:"timestamp"`
	// 版本 | Version
	Version string `json:"version"`
	// 服务信息 | Service info
	ServiceInfo ServiceInfo `json:"serviceInfo"`
}

// 服务信息 | Service info
// swagger:model ServiceInfo
type ServiceInfo struct {
	// 服务名称 | Service name
	Name string `json:"name"`
	// 构建版本 | Build version
	BuildVersion string `json:"buildVersion"`
	// 构建时间 | Build time
	BuildTime string `json:"buildTime"`
	// Go版本 | Go version
	GoVersion string `json:"goVersion"`
}
