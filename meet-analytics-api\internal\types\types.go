// Code generated by goctl. DO NOT EDIT.
package types

type BaseDataInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data,omitempty"`
}

type BaseIDInfo struct {
	Id        *uint64 `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type BaseIDInt32Info struct {
	Id        *int32 `json:"id,optional"`
	CreatedAt *int64 `json:"createdAt,optional"`
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

type BaseIDInt64Info struct {
	Id        *int64 `json:"id,optional"`
	CreatedAt *int64 `json:"createdAt,optional"`
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

type BaseIDStringInfo struct {
	Id        *string `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type BaseIDUint32Info struct {
	Id        *uint32 `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type BaseListInfo struct {
	Total uint64 `json:"total"`
	Data  string `json:"data,omitempty"`
}

type BaseMsgResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type BaseUUIDInfo struct {
	Id        *string `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type EmptyReq struct {
}

type EventError struct {
	Index        int32  `json:"index"`
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

type EventItem struct {
	EventName        string   `json:"eventName" validate:"required"`
	UserId           *uint64  `json:"userId,optional"`
	DeviceId         string   `json:"deviceId" validate:"required"`
	PageUrl          *string  `json:"pageUrl,optional"`
	PageTitle        *string  `json:"pageTitle,optional"`
	ReferrerUrl      *string  `json:"referrerUrl,optional"`
	UtmSource        *string  `json:"utmSource,optional"`
	UtmMedium        *string  `json:"utmMedium,optional"`
	UtmCampaign      *string  `json:"utmCampaign,optional"`
	UtmTerm          *string  `json:"utmTerm,optional"`
	UtmContent       *string  `json:"utmContent,optional"`
	EventProperties  *string  `json:"eventProperties,optional"`
	CustomProperties *string  `json:"customProperties,optional"`
	Timestamp        *int64   `json:"timestamp,optional"`
	IpAddress        *string  `json:"ipAddress,optional"`
	UserAgent        *string  `json:"userAgent,optional"`
	AppVersion       *string  `json:"appVersion,optional"`
	OsVersion        *string  `json:"osVersion,optional"`
	Platform         *string  `json:"platform,optional"`
	DeviceBrand      *string  `json:"deviceBrand,optional"`
	DeviceModel      *string  `json:"deviceModel,optional"`
	Country          *string  `json:"country,optional"`
	Province         *string  `json:"province,optional"`
	City             *string  `json:"city,optional"`
	Latitude         *float64 `json:"latitude,optional"`
	Longitude        *float64 `json:"longitude,optional"`
}

type EventReportResp struct {
	BaseDataInfo
	Data EventReportResult `json:"data"`
}

type EventReportResult struct {
	SuccessCount int32        `json:"successCount"`
	FailedCount  int32        `json:"failedCount"`
	ProcessedAt  int64        `json:"processedAt"`
	Errors       []EventError `json:"errors,optional"`
}

type HealthCheckResp struct {
	Status      string      `json:"status"`
	Timestamp   int64       `json:"timestamp"`
	Version     string      `json:"version"`
	ServiceInfo ServiceInfo `json:"serviceInfo"`
}

type IDInt32PathReq struct {
	Id int32 `path:"id"`
}

type IDInt32Req struct {
	Id int32 `json:"id" validate:"number"`
}

type IDInt64PathReq struct {
	Id int64 `path:"id"`
}

type IDInt64Req struct {
	Id int64 `json:"id" validate:"number"`
}

type IDPathReq struct {
	Id uint64 `path:"id"`
}

type IDReq struct {
	Id uint64 `json:"id" validate:"number"`
}

type IDStringPathReq struct {
	Id string `path:"id"`
}

type IDStringReq struct {
	Id string `json:"id"`
}

type IDUint32PathReq struct {
	Id uint32 `path:"id"`
}

type IDUint32Req struct {
	Id uint32 `json:"id" validate:"number"`
}

type IDsInt32Req struct {
	Ids []int32 `json:"ids"`
}

type IDsInt64Req struct {
	Ids []int64 `json:"ids"`
}

type IDsReq struct {
	Ids []uint64 `json:"ids"`
}

type IDsStringReq struct {
	Ids []string `json:"ids"`
}

type IDsUint32Req struct {
	Ids []uint32 `json:"ids"`
}

type PageInfo struct {
	Page     uint64 `json:"page" validate:"required,number,gt=0"`
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

type ServiceInfo struct {
	Name         string `json:"name"`
	BuildVersion string `json:"buildVersion"`
	BuildTime    string `json:"buildTime"`
	GoVersion    string `json:"goVersion"`
}

type UUIDPathReq struct {
	Id string `path:"id"`
}

type UUIDReq struct {
	Id string `json:"id" validate:"required,len=36"`
}

type UUIDsReq struct {
	Ids []string `json:"ids"`
}

type UniversalReportReq struct {
	Events []EventItem `json:"events" validate:"required,min=1,max=100"`
}
