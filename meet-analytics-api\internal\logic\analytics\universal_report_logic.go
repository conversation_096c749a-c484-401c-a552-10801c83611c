package analytics

import (
	"context"
	"net"
	"net/http"
	"regexp"
	"strings"
	"time"

	"seevision.cn/server/meet-analytics-api/internal/middleware"
	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UniversalReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUniversalReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UniversalReportLogic {
	return &UniversalReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UniversalReportLogic) UniversalReport(req *types.UniversalReportReq, r *http.Request) (resp *types.EventReportResp, err error) {
	l.Infof("=== 收到事件上报请求 ===")
	l.Infof("请求事件数量: %d", len(req.Events))
	l.Infof("请求URL: %s", r.URL.Path)
	l.Infof("请求方法: %s", r.Method)

	// 初始化响应
	resp = &types.EventReportResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  "success",
		},
		Data: types.EventReportResult{
			SuccessCount: 0,
			FailedCount:  0,
			ProcessedAt:  time.Now().UnixMilli(),
			Errors:       []types.EventError{},
		},
	}

	// 验证事件数量
	if len(req.Events) == 0 {
		resp.BaseDataInfo.Code = 400
		resp.BaseDataInfo.Msg = "事件列表不能为空"
		return resp, nil
	}

	if len(req.Events) > 100 {
		resp.BaseDataInfo.Code = 400
		resp.BaseDataInfo.Msg = "单次上报事件数量不能超过100个"
		return resp, nil
	}

	// 验证平台参数格式
	for _, event := range req.Events {
		if event.AppVersion != nil && *event.AppVersion != "" && !isValidVersion(*event.AppVersion) {
			resp.BaseDataInfo.Code = 400
			resp.BaseDataInfo.Msg = "应用版本格式无效，应为x.y.z格式"
			return resp, nil
		}
		if event.OsVersion != nil && *event.OsVersion != "" && !isValidVersion(*event.OsVersion) {
			resp.BaseDataInfo.Code = 400
			resp.BaseDataInfo.Msg = "操作系统版本格式无效"
			return resp, nil
		}
		if event.Platform != nil && *event.Platform != "" && !isValidPlatform(*event.Platform) {
			resp.BaseDataInfo.Code = 400
			resp.BaseDataInfo.Msg = "平台参数无效，应为iOS/Android/Web"
			return resp, nil
		}
	}

	// 从上下文中获取appId
	appId := middleware.GetAppIdFromCtx(l.ctx)
	l.Infof("从上下文获取到的appId: %d", appId)
	if appId == 0 {
		l.Errorf("无法获取有效的appId，上下文可能缺少应用凭证信息")
		resp.BaseDataInfo.Code = 401
		resp.BaseDataInfo.Msg = "无效的应用凭证"
		return resp, nil
	}

	// 提取HTTP请求信息
	clientIP := l.getClientIP(r)
	userAgent := r.Header.Get("User-Agent")
	l.Infof("提取请求信息 - ClientIP: %s, UserAgent: %s", clientIP, userAgent)

	// 创建采样逻辑实例
	l.Infof("开始创建采样逻辑实例")
	samplingLogic := NewSamplingLogic(l.ctx, l.svcCtx)
	l.Infof("采样逻辑实例创建成功")

	// 第一阶段：采样率控制和预处理
	l.Infof("开始第一阶段：采样率控制和预处理，事件数量: %d", len(req.Events))
	sampledEvents := make([]types.EventItem, 0, len(req.Events))
	skippedCount := 0

	for i, event := range req.Events {
		l.Infof("处理事件 %d: EventName=%s, DeviceId=%s",
			i, event.EventName, event.DeviceId)

		// 调试：检查 EventProperties 字段
		if event.EventProperties != nil {
			l.Infof("事件 %d EventProperties 值: %s", i, *event.EventProperties)
		} else {
			l.Infof("事件 %d EventProperties 为 nil", i)
		}

		// 采样决策
		samplingResult := samplingLogic.ShouldSampleEvent(&event, clientIP)
		l.Infof("事件 %d 采样决策完成: ShouldSample=%t, SamplingRate=%.4f",
			i, samplingResult.ShouldSample, samplingResult.SamplingRate)

		if samplingResult.ShouldSample {
			sampledEvents = append(sampledEvents, event)
			l.Infof("事件 %d 采样通过: %s", i, samplingResult.SamplingReason)
		} else {
			skippedCount++
			l.Infof("事件 %d 被采样跳过: %s", i, samplingResult.SkipReason)
		}
	}

	l.Infof("采样控制完成，原始事件: %d, 采样事件: %d, 跳过事件: %d",
		len(req.Events), len(sampledEvents), skippedCount)

	// 如果没有事件需要处理，直接返回成功
	if len(sampledEvents) == 0 {
		l.Infof("所有事件均被采样控制跳过，直接返回成功")
		resp.BaseDataInfo.Msg = "所有事件均被采样控制跳过"
		return resp, nil
	}

	// 第二阶段：批量处理采样后的事件
	l.Infof("开始第二阶段：批量处理采样后的事件，数量: %d", len(sampledEvents))
	batchLogic := NewBatchProcessingLogic(l.ctx, l.svcCtx)
	l.Infof("批量处理逻辑实例创建成功，开始处理事件")

	batchResult, err := batchLogic.ProcessEventsBatch(sampledEvents, clientIP, userAgent, appId)
	if err != nil {
		l.Errorf("批量处理失败: %v", err)
		resp.BaseDataInfo.Code = 500
		resp.BaseDataInfo.Msg = "批量处理失败: " + err.Error()
		return resp, nil
	}
	l.Infof("批量处理完成，结果: 成功=%d, 失败=%d", batchResult.SuccessCount, batchResult.FailedCount)

	// 更新响应结果
	resp.Data.SuccessCount = int32(batchResult.SuccessCount)
	resp.Data.FailedCount = int32(batchResult.FailedCount)
	resp.Data.Errors = batchResult.Errors

	// 如果有验证错误，返回400状态码
	if batchResult.FailedCount > 0 && len(batchResult.Errors) > 0 {
		// 检查是否有验证错误（如DeviceId为空等）
		for _, err := range batchResult.Errors {
			if strings.Contains(err.ErrorMessage, "验证失败") ||
				strings.Contains(err.ErrorMessage, "不能为空") ||
				strings.Contains(err.ErrorMessage, "DeviceId") {
				resp.BaseDataInfo.Code = 400
				resp.BaseDataInfo.Msg = "事件验证失败，请检查必填字段"
				l.Errorf("事件验证失败，返回400状态码: %v", batchResult.Errors)
				return resp, nil
			}
		}
	}

	// 记录详细处理结果
	l.Infof("事件上报处理完成 - 原始: %d, 采样: %d, 跳过: %d, 成功: %d, 失败: %d, 处理耗时: %v",
		len(req.Events), len(sampledEvents), skippedCount,
		resp.Data.SuccessCount, resp.Data.FailedCount, batchResult.ProcessTime)

	return resp, nil
}

// getClientIP 从请求中提取客户端IP地址
// 优先从 X-Forwarded-For -> X-Real-IP -> RemoteAddr 获取
func (l *UniversalReportLogic) getClientIP(r *http.Request) string {
	// 尝试从 X-Forwarded-For 获取
	forwardedFor := r.Header.Get("X-Forwarded-For")
	if forwardedFor != "" {
		// X-Forwarded-For 可能包含多个IP，第一个通常是客户端的真实IP
		ips := strings.Split(forwardedFor, ",")
		if len(ips) > 0 {
			clientIP := strings.TrimSpace(ips[0])
			if clientIP != "" {
				return clientIP
			}
		}
	}

	// 尝试从 X-Real-IP 获取
	realIP := r.Header.Get("X-Real-IP")
	if realIP != "" {
		return realIP
	}

	// 最后回退到 RemoteAddr
	// RemoteAddr 可能包含端口号，需要移除
	if ip, _, err := net.SplitHostPort(r.RemoteAddr); err == nil {
		return ip
	}

	return r.RemoteAddr
}

// isValidVersion 验证版本号格式 (支持 x, x.y, x.y.z 格式)
func isValidVersion(version string) bool {
	// 支持更灵活的版本格式：
	// - 单个数字：11, 14
	// - 两段版本：1.0, 11.0
	// - 三段版本：1.0.0, 11.0.1
	return regexp.MustCompile(`^\d+(\.\d+){0,2}$`).MatchString(version)
}

// isValidPlatform 验证平台名称
func isValidPlatform(platform string) bool {
	validPlatforms := map[string]bool{
		"iOS":     true,
		"Android": true,
		"Web":     true,
	}
	return validPlatforms[platform]
}
