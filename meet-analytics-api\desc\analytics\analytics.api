import "../base.api"

type (
	// 通用事件上报请求 | Universal event report request
	UniversalReportReq {
		// 事件列表 | Event list
		Events []EventItem `json:"events" validate:"required,min=1,max=100"`
	}
	// 事件项 | Event item
	EventItem {
		// 事件名称 | Event name
		EventName string `json:"eventName" validate:"required"`
		// 用户ID | User ID
		UserId *uint64 `json:"userId,optional"`
		// 设备ID | Device ID
		DeviceId string `json:"deviceId" validate:"required"`
		// 页面URL | Page URL
		PageUrl *string `json:"pageUrl,optional"`
		// 页面标题 | Page title
		PageTitle *string `json:"pageTitle,optional"`
		// 来源页面 | Referrer URL
		ReferrerUrl *string `json:"referrerUrl,optional"`
		// UTM来源 | UTM source
		UtmSource *string `json:"utmSource,optional"`
		// UTM媒介 | UTM medium
		UtmMedium *string `json:"utmMedium,optional"`
		// UTM活动 | UTM campaign
		UtmCampaign *string `json:"utmCampaign,optional"`
		// UTM关键词 | UTM term
		UtmTerm *string `json:"utmTerm,optional"`
		// UTM内容 | UTM content
		UtmContent *string `json:"utmContent,optional"`
		// 事件属性 | Event properties (JSON格式)
		EventProperties *string `json:"eventProperties,optional"`
		// 自定义属性 | Custom properties (JSON格式)
		CustomProperties *string `json:"customProperties,optional"`
		// 事件发生时间戳(毫秒) | Event timestamp in milliseconds
		Timestamp *int64 `json:"timestamp,optional"`
		// IP地址 | IP address (服务端可自动获取)
		IpAddress *string `json:"ipAddress,optional"`
		// User Agent (服务端可自动获取)
		UserAgent *string `json:"userAgent,optional"`
		// 应用版本 | App version
		AppVersion *string `json:"appVersion,optional"`
		// 操作系统版本 | OS version
		OsVersion *string `json:"osVersion,optional"`
		// 平台 | Platform
		Platform *string `json:"platform,optional"`
		// 设备品牌 | Device brand (Apple, Huawei, Xiaomi, Samsung等)
		DeviceBrand *string `json:"deviceBrand,optional"`
		// 设备型号 | Device model (iPhone 13, Huawei P40, Xiaomi Mi 11等)
		DeviceModel *string `json:"deviceModel,optional"`
		// 国家 | Country
		Country *string `json:"country,optional"`
		// 省份 | Province
		Province *string `json:"province,optional"`
		// 城市 | City
		City *string `json:"city,optional"`
		// 纬度 | Latitude
		Latitude *float64 `json:"latitude,optional"`
		// 经度 | Longitude
		Longitude *float64 `json:"longitude,optional"`
	}
	// 事件上报响应 | Event report response
	EventReportResp {
		BaseDataInfo
		// 处理结果 | Processing result
		Data EventReportResult `json:"data"`
	}
	// 事件上报结果 | Event report result
	EventReportResult {
		// 成功处理的事件数量 | Successfully processed events count
		SuccessCount int32 `json:"successCount"`
		// 失败的事件数量 | Failed events count
		FailedCount int32 `json:"failedCount"`
		// 处理时间戳 | Processing timestamp
		ProcessedAt int64 `json:"processedAt"`
		// 错误信息 | Error details
		Errors []EventError `json:"errors,optional"`
	}
	// 事件错误信息 | Event error info
	EventError {
		// 事件索引 | Event index
		Index int32 `json:"index"`
		// 错误码 | Error code
		ErrorCode string `json:"errorCode"`
		// 错误信息 | Error message
		ErrorMessage string `json:"errorMessage"`
	}
	// 健康检查响应 | Health check response
	HealthCheckResp {
		// 状态 | Status
		Status string `json:"status"`
		// 时间戳 | Timestamp
		Timestamp int64 `json:"timestamp"`
		// 版本 | Version
		Version string `json:"version"`
		// 服务信息 | Service info
		ServiceInfo ServiceInfo `json:"serviceInfo"`
	}
	// 服务信息 | Service info
	ServiceInfo {
		// 服务名称 | Service name
		Name string `json:"name"`
		// 构建版本 | Build version
		BuildVersion string `json:"buildVersion"`
		// 构建时间 | Build time
		BuildTime string `json:"buildTime"`
		// Go版本 | Go version
		GoVersion string `json:"goVersion"`
	}
)

@server (
	group:      analytics
	prefix:     /app/v1/analytics
	middleware: SignMiddleware
)
service MeetAnalyticsApi {
	// 通用事件上报 | Universal event report
	// 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等
	@handler UniversalReportHandler
	post /report (UniversalReportReq) returns (EventReportResp)
}

@server (
	group:  analytics
	prefix: /app/v1/analytics
)
service MeetAnalyticsApi {
	// 健康检查 | Health check
	@handler HealthCheckHandler
	get /health returns (HealthCheckResp)
}

