package svc

import (
	"github.com/zeromicro/go-zero/rest"
	"seevision.cn/server/meet-analytics-api/internal/config"
	"seevision.cn/server/meet-analytics-api/internal/middleware"
)

type ServiceContext struct {
	Config         config.Config
	SignMiddleware rest.Middleware
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:         c,
		SignMiddleware: middleware.NewSignMiddleware().Handle,
	}
}
