package analytics

import (
	"context"

	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UniversalReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUniversalReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UniversalReportLogic {
	return &UniversalReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UniversalReportLogic) UniversalReport(req *types.UniversalReportReq) (resp *types.EventReportResp, err error) {
	// todo: add your logic here and delete this line

	return
}
