package analytics

import (
	"encoding/json"
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"

	"seevision.cn/server/meet-analytics-api/internal/logic/analytics"
	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"
)

// swagger:route post /app/v1/analytics/report analytics UniversalReport
//
// 通用事件上报 | Universal event report // 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等
//
// 通用事件上报 | Universal event report // 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UniversalReportReq
//
// Responses:
//  200: EventReportResp

func UniversalReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UniversalReportReq
		if err := httpx.Parse(r, &req, false); err != nil {
			logx.Errorw("UniversalReportHandler Parse error:", logx.Field("error", err))
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := analytics.NewUniversalReportLogic(r.Context(), svcCtx)
		resp, err := l.UniversalReport(&req, r)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
